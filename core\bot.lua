local WebSocket = require('./core/websocket')
local Emitter = require('core').Emitter

local Bot = {}
Bot.__index = Bot

function Bot:new(config)
    local self = setmetatable({}, Bot)
    self.config = config
    self.ws = nil
    self.events = Emitter:new()
    self.plugins = {}
    return self
end

function Bot:connect()
    self.ws = WebSocket:new(self.config.ws_url)
    self.ws:on('message', function(data)
        self:handleMessage(data)
    end)
    self.ws:connect()
end

function Bot:handleMessage(data)
    local msg = require('json').parse(data)
    self.events:emit(msg.post_type, msg)
end

function Bot:sendMessage(params)
    local data = {
        action = "send_msg",
        params = params
    }
    self.ws:send(require('json').stringify(data))
end

return Bot
