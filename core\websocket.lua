local net = require('net')
local http = require('http')
local crypto = require('crypto')
local bit = require('bit')
local Emitter = require('core').Emitter

local WSClient = {}
WSClient.__index = WSClient

function WSClient:new(url)
    local self = setmetatable({}, WSClient)
    self.url = url
    self.socket = nil
    self.events = Emitter:new()
    self.connected = false
    return self
end

-- 简单的WebSocket握手
function WSClient:connect()
    local url_parts = self:parseUrl(self.url)

    self.socket = net.createConnection(url_parts.port, url_parts.host, function()
        self:performHandshake(url_parts)
    end)

    self.socket:on('data', function(data)
        if not self.connected then
            self:handleHandshakeResponse(data)
        else
            self:handleWebSocketFrame(data)
        end
    end)

    self.socket:on('error', function(err)
        self.events:emit('error', err)
    end)

    self.socket:on('close', function()
        self.events:emit('close')
    end)
end

function WSClient:parseUrl(url)
    local host, port, path = url:match("ws://([^:/]+):?(%d*)(.*)")
    return {
        host = host,
        port = tonumber(port) or 80,
        path = path ~= "" and path or "/"
    }
end

function WSClient:performHandshake(url_parts)
    local key = crypto.randomBytes(16):toString('base64')
    local request = string.format(
        "GET %s HTTP/1.1\r\n" ..
        "Host: %s:%d\r\n" ..
        "Upgrade: websocket\r\n" ..
        "Connection: Upgrade\r\n" ..
        "Sec-WebSocket-Key: %s\r\n" ..
        "Sec-WebSocket-Version: 13\r\n" ..
        "\r\n",
        url_parts.path, url_parts.host, url_parts.port, key
    )
    self.socket:write(request)
end

function WSClient:handleHandshakeResponse(data)
    if data:find("HTTP/1.1 101") then
        self.connected = true
        self.events:emit('open')
    else
        self.events:emit('error', 'WebSocket handshake failed')
    end
end

function WSClient:handleWebSocketFrame(data)
    -- 简化的帧解析，只处理文本帧
    if #data >= 2 then
        local payload_len = bit.band(data:byte(2), 0x7F)
        local payload_start = 3

        if payload_len == 126 then
            payload_start = 5
        elseif payload_len == 127 then
            payload_start = 11
        end

        if #data >= payload_start then
            local payload = data:sub(payload_start)
            self.events:emit('message', payload)
        end
    end
end

function WSClient:send(data)
    if self.connected and self.socket then
        -- 构造WebSocket文本帧
        local frame = self:createTextFrame(data)
        self.socket:write(frame)
    end
end

function WSClient:createTextFrame(data)
    local len = #data
    local frame = string.char(0x81) -- FIN=1, opcode=1 (text)

    if len < 126 then
        frame = frame .. string.char(bit.bor(len, 0x80)) -- MASK=1
    elseif len < 65536 then
        frame = frame .. string.char(0xFE) .. string.char(bit.rshift(len, 8)) .. string.char(bit.band(len, 0xFF))
    end

    -- 简化：使用固定mask
    local mask = { 0x12, 0x34, 0x56, 0x78 }
    frame = frame .. string.char(mask[1], mask[2], mask[3], mask[4])

    -- 应用mask
    local masked_data = ""
    for i = 1, len do
        local byte = data:byte(i)
        local masked_byte = bit.bxor(byte, mask[((i - 1) % 4) + 1])
        masked_data = masked_data .. string.char(masked_byte)
    end

    return frame .. masked_data
end

function WSClient:on(event, callback)
    self.events:on(event, callback)
end

function WSClient:close()
    if self.socket then
        self.socket:close()
    end
end

return WSClient
